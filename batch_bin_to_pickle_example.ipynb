import sys
import os
from pathlib import Path

from module.race_data_processor import RaceProcessor

import glob
import pandas as pd

# 処理したい年のリストを指定
years = ['2017', '2018', '2019']  # 必要な年を追加してください
# 単一年の場合: years = ['2024']
# 複数年の場合: years = ['2020', '2021', '2022', '2023', '2024']

# データディレクトリの設定
base_dir = Path.cwd()  # 現在のディレクトリを基準
data_dir = base_dir / 'data' / 'html' / 'race' / 'race_by_year'

# 出力ディレクトリの作成
output_dir = Path('output')
output_dir.mkdir(exist_ok=True)

print(f"処理対象年: {years}")
print(f"データディレクトリ: {data_dir}")
print(f"出力ディレクトリ: {output_dir}")

processor = RaceProcessor()

for year in years:
    print(f'{year}年のbinファイルを処理中...')
    bin_dir = data_dir / year
    bin_files = list(bin_dir.glob('*.bin'))
    race_info_list = []
    race_results_list = []
    for bin_file in bin_files:
        info_df, results_df = processor.parse_race_html(html_path=bin_file)
        if not info_df.empty:
            race_info_list.append(info_df)
        if not results_df.empty:
            race_results_list.append(results_df)
    # 年ごとにまとめてDataFrame化
    race_info_df = pd.concat(race_info_list, ignore_index=True) if race_info_list else pd.DataFrame()
    race_results_df = pd.concat(race_results_list, ignore_index=True) if race_results_list else pd.DataFrame()
    # DataFrameが空でなければ保存
    if not race_info_df.empty:
        race_info_df.to_pickle(output_dir / f'race_info_{year}.pickle')
        print(f'  race_info_{year}.pickle を保存')
    if not race_results_df.empty:
        race_results_df.to_pickle(output_dir / f'race_results_{year}.pickle')
        print(f'  race_results_{year}.pickle を保存')
    print(f'{year}年のpickle保存完了\n')

print('全ての年の処理が完了しました。')

import module.race_batch_processor
# 使用例1: binファイルからpickleファイルへの変換
years = ["2024"]
module.race_batch_processor.process_race_bin_to_pickle_batch(
    years=years,
    bin_base_dir=data_dir,
    output_dir=output_dir,
    parallel=True,
    max_workers=4
)

from module.race_horse_targeted_processor import RaceHorseTargetedProcessor

# プロセッサ作成
processor = RaceHorseTargetedProcessor()

# 2024年のレースから馬IDを抽出し、馬情報を処理
result = processor.process_race_to_horses(
    year="2023",
    include_basic_info=True,
    include_results=True,
    parallel=True,
    max_workers=4,
    save_output=True
)

race_info = pd.read_pickle(output_dir / "race_info_2024.pickle")
race_info

race_results = pd.read_pickle(output_dir / "race_results_2024.pickle")
race_results

horse_info = pd.read_pickle(output_dir / "race_horses_horse_info_2024.pickle")
horse_info

horse_results = pd.read_pickle(output_dir / "race_horses_horse_results_2024.pickle")
horse_results

