#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
包括のデータ統合モジュール
レース情報、レース結果、馬基本情報、馬過去成績を統合した
包括的な表形式データを生成する
"""

import argparse # main関数で使用
import os
import pandas as pd
import datetime
import json # 設定ファイル読み込み用
import logging # ロギング設定はmain関数で行う
from typing import Optional, Dict, Any, List, Tuple

from module.race_data_processor import RaceProcessor
from module.horse_processor import HorseProcessor
from module.constants import LocalPaths
from tqdm.auto import tqdm # tqdm は _merge_horse_performance_stats 内の RaceProcessor で使用 # type: ignore
import dataclasses # dataclasses.fields を使用するため

from module.constants import ComprehensiveIntegratorConfig # dataclassとしてインポート

class ComprehensiveDataIntegrator:
    """
    レース情報、レース結果、馬基本情報、馬過去成績を統合した
    包括的な表形式データを生成するクラス
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初期化

        Parameters
        ----------
        config : Dict[str, Any], optional
            設定情報
            例: {"max_workers": 4, "default_year": "2023", ...}
        """
        self.logger = logging.getLogger(__name__)

        # dataclassを使用して設定を初期化し、バリデーション（__post_init__）
        try:
            if config is not None:
                # config 辞書に含まれるキーのみを ComprehensiveIntegratorConfig に渡す
                # ComprehensiveIntegratorConfig のフィールド名と一致するキーのみを抽出
                valid_keys = {f.name for f in dataclasses.fields(ComprehensiveIntegratorConfig)}
                filtered_config = {k: v for k, v in config.items() if k in valid_keys}
                self.config = ComprehensiveIntegratorConfig(**filtered_config)
            else:
                self.config = ComprehensiveIntegratorConfig() # デフォルト値で初期化
            self.logger.info("設定の初期化とバリデーションに成功しました。")
        except (ValueError, TypeError) as e: # __post_init__ からの ValueError や、引数不一致の TypeError
            self.logger.error(f"設定の初期化/バリデーションに失敗しました: {e}")
            # バリデーション失敗時は、デフォルト設定で続行するか、例外を発生させるか検討
            try:
                self.config = ComprehensiveIntegratorConfig() # デフォルト設定で再試行
                self.logger.warning("エラーのため、デフォルト設定でConfigを初期化しました。")
            except Exception as init_e:
                self.logger.critical(f"デフォルト設定でのConfig初期化も失敗しました: {init_e}")
                raise # クリティカルなエラーとして上位に伝播

        # プロセッサの初期化
        self.race_processor = RaceProcessor()
        self.horse_processor = HorseProcessor()

        # データ保持用
        self._race_info_df = pd.DataFrame()
        self._race_results_df = pd.DataFrame()
        self._horse_info_df = pd.DataFrame()
        self._horse_results_df = pd.DataFrame()
        self._comprehensive_df = pd.DataFrame()



    def generate_comprehensive_table(self,
                                   year: Optional[str] = None,
                                   years: Optional[List[str]] = None,
                                   race_id: Optional[str] = None,
                                   include_race_info: Optional[bool] = None,
                                   include_horse_info: Optional[bool] = None,
                                   include_past_performance: Optional[bool] = None,
                                   performance_window_races: Optional[List[int]] = None,
                                   parallel: Optional[bool] = None,
                                   max_workers: Optional[int] = None) -> pd.DataFrame: # メソッド引数はユーザーが明示的に指定したい場合用
        """
        包括的な表形式データを生成

        Parameters
        ----------
        year : str, optional
            処理する年度（単一年度）
        years : List[str], optional
            処理する年度（複数年度）
        race_id : str, optional
            特定のレースID
        include_race_info : bool, default True
            レース情報（天気、距離、馬場状態等）を含めるか
        include_horse_info : bool, default True
            馬基本情報（血統、調教師、馬主等）を含めるか
        include_past_performance : bool, default True
            馬の過去成績統計を含めるか
        performance_window_races : List[int], default [5, 10]
            過去成績の集計対象レース数
        parallel : bool, default True
            並列処理を使用するか
        max_workers : int, optional
            並列処理の最大ワーカー数

        Returns
        -------
        pd.DataFrame
            包括的な統合データ
        """
        self.logger.info("包括的な表形式データの生成を開始します")

        # 実行時の設定を構築 (メソッド引数で self.config を上書きした新しいConfigインスタンスを作成)
        effective_config_dict = dataclasses.asdict(self.config)
        if year is not None: effective_config_dict['default_year'] = year
        if years is not None: effective_config_dict['default_years'] = years
        if race_id is not None: effective_config_dict['race_id'] = race_id
        if include_race_info is not None: effective_config_dict['include_race_info'] = include_race_info
        if include_horse_info is not None: effective_config_dict['include_horse_info'] = include_horse_info
        if include_past_performance is not None: effective_config_dict['include_past_performance'] = include_past_performance
        if performance_window_races is not None: effective_config_dict['performance_window_races'] = performance_window_races
        if parallel is not None: effective_config_dict['parallel'] = parallel
        if max_workers is not None: effective_config_dict['max_workers'] = max_workers
        
        try:
            effective_config = ComprehensiveIntegratorConfig(**effective_config_dict)
        except (ValueError, TypeError) as e:
            self.logger.error(f"実行時設定の構築に失敗: {e}。インスタンスのデフォルト設定を使用します。")
            effective_config = self.config

        # 年度の処理（複数年度対応）
        # race_id が指定されていれば、それを最優先
        if effective_config.race_id:
            year_from_race_id = effective_config.race_id[:4]
            return self._generate_single_year_table(year_from_race_id, effective_config)
        
        target_years_list = effective_config.default_years if effective_config.default_years is not None else []
        if not isinstance(target_years_list, list): target_years_list = [target_years_list] # 単一年の場合もリスト化

        if len(target_years_list) == 1:
            return self._generate_single_year_table(target_years_list[0], effective_config)
        else:
            return self._generate_multi_year_table(target_years_list, effective_config)

    def _generate_single_year_table(self, year: str, effective_config: ComprehensiveIntegratorConfig) -> pd.DataFrame:
        """
        単一年度の包括的データを生成
        """
        # 1. レース情報とレース結果の取得
        self.logger.info("1. レース情報とレース結果を取得中...")
        race_info_df, race_results_df = self.race_processor.process_race_bin_files(
            year=year, # 年度は必須
            race_id=effective_config.race_id,
            parallel=effective_config.parallel,
            max_workers=effective_config.max_workers
        )

        if race_results_df.empty:
            self.logger.warning("レース結果データが空です")
            return pd.DataFrame()

        self._race_info_df = race_info_df
        self._race_info_df = self._standardize_data_types(self._race_info_df)
        self._race_results_df = race_results_df
        self._race_results_df = self._standardize_data_types(self._race_results_df)

        self.logger.info(f"レース情報: {len(race_info_df)}件")
        self.logger.info(f"レース結果: {len(race_results_df)}件")

        # 2. ベースデータの作成（レース結果をベースとする）
        self.logger.info("2. ベースデータを作成中...")
        comprehensive_df = race_results_df.copy()
        comprehensive_df = self._standardize_data_types(comprehensive_df) # 型標準化

        # 3. レース情報の統合 (設定値はPydanticモデルから取得)
        if effective_config.include_race_info and not race_info_df.empty:
            self.logger.info("3. レース情報を統合中...")
            comprehensive_df = self._merge_race_info(comprehensive_df, race_info_df)
            self.logger.info(f"レース情報統合後: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")

        # 4. 馬基本情報の統合 (設定値はPydanticモデルから取得)
        if effective_config.include_horse_info:
            self.logger.info("4. 馬基本情報を統合中...")
            comprehensive_df = self._merge_horse_info(comprehensive_df, year=year, target_horse_ids=self._extract_horse_ids_from_race_data(self._race_results_df))
            self.logger.info(f"馬基本情報統合後: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")

        # 5. 馬過去成績統計の統合 (設定値はPydanticモデルから取得)
        if effective_config.include_past_performance:
            self.logger.info("5. 馬過去成績統計を統合中...")
            comprehensive_df = self._merge_horse_performance_stats(
                comprehensive_df,
                performance_window_races=effective_config.performance_window_races,
                max_workers=effective_config.max_workers,
                target_horse_ids=self._extract_horse_ids_from_race_data(self._race_results_df)
            )
            self.logger.info(f"過去成績統計統合後: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")

        self._comprehensive_df = comprehensive_df
        self.logger.info(f"包括的データ生成完了: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")
        return comprehensive_df

    def _generate_multi_year_table(self, target_years: List[str], effective_config: ComprehensiveIntegratorConfig) -> pd.DataFrame:
        """
        複数年度の包括的データを生成
        """
        self.logger.info(f"複数年度データ統合開始: {target_years}")

        all_dfs = []

        for year in tqdm(target_years, desc="複数年度データ統合中"):
            self.logger.info(f"年度 {year} を処理中...")

            year_df = self._generate_single_year_table(year, effective_config)

            if not year_df.empty:
                # 年度情報を追加
                year_df['year'] = year
                all_dfs.append(year_df)
                self.logger.info(f"年度 {year}: {len(year_df)}件のデータを取得")
            else:
                self.logger.warning(f"年度 {year}: データが空です")

        if all_dfs:
            # 全年度のデータを結合
            comprehensive_df = pd.concat(all_dfs, ignore_index=True)
            self._comprehensive_df = comprehensive_df
            self.logger.info(f"複数年度統合完了: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")
            return comprehensive_df
        else:
            self.logger.warning("全年度でデータが空です")
            return pd.DataFrame()

    def _merge_race_info(self, base_df: pd.DataFrame, race_info_df: pd.DataFrame) -> pd.DataFrame:
        """
        レース情報をベースデータに統合

        Parameters
        ----------
        base_df : pd.DataFrame
            ベースとなるレース結果データ
        race_info_df : pd.DataFrame
            レース情報データ

        Returns
        -------
        pd.DataFrame
            レース情報が統合されたデータ
        """
        try:
            if 'race_id' in base_df.columns and 'race_id' in race_info_df.columns:
                merged_df = base_df.merge(
                    race_info_df,
                    on='race_id',
                    how='left',
                    suffixes=('', '_race_info')
                )
                return merged_df
            else:
                self.logger.warning("race_idカラムが見つからないため、レース情報の統合をスキップします")
                return base_df
        except Exception as e:
            self.logger.error(f"レース情報統合エラー: {e}")
            return base_df

    def _merge_horse_info(self, base_df: pd.DataFrame, year: Optional[str] = None, target_horse_ids: Optional[List[str]] = None) -> pd.DataFrame:
        """
        馬基本情報をベースデータに統合

        Parameters
        ----------
        base_df : pd.DataFrame
            ベースデータ
        year : str, optional
            対象年度
        target_horse_ids : List[str], optional
            処理対象の馬IDリスト。指定されていれば、これらの馬の情報のみ取得・マージする.

        Returns
        -------
        pd.DataFrame
            馬基本情報が統合されたデータ
        """
        try:
            if not target_horse_ids:
                self.logger.warning("対象の馬IDが指定されていないため、馬基本情報の統合をスキップします。")
                return base_df

            # 馬基本情報を取得（レースに出走した馬のみ）
            # HorseProcessorのget_rawdata_horse_infoはHTMLパスリストを引数に取るため、
            # ここでは、対象馬IDのHTMLファイルパスを特定し、それを渡すか、
            # HorseProcessorにIDリストから直接情報を取得するメソッドを期待する。
            # HorseProcessorのget_horse_infoはhorse_id_listを引数に取る。year引数はHorseProcessor側で現在未使用。
            horse_info_df = self.horse_processor.get_horse_info(horse_id_list=target_horse_ids)

            if horse_info_df.empty:
                self.logger.warning("馬基本情報データが空のため、統合をスキップします")
                return base_df

            horse_info_df = self._standardize_data_types(horse_info_df) # 型標準化
            # horse_idでマージ
            if 'horse_id' in base_df.columns:
                # horse_idの型を統一
                base_df['horse_id'] = base_df['horse_id'].astype(str)
                horse_info_df.index = horse_info_df.index.astype(str)
                
                # 重複する可能性のあるカラムをリネーム (例: 調教師、馬主など)
                # horse_info_df = horse_info_df.rename(columns=lambda c: c + '_info' if c in base_df.columns and c != 'horse_id' else c)
                
                merged_df = base_df.merge(
                    horse_info_df,
                    left_on='horse_id',
                    right_index=True,
                    how='left',
                    suffixes=('', '_horse_info')
                )
                return merged_df
            else:
                self.logger.warning("horse_idカラムが見つからないため、馬基本情報の統合をスキップします")
                return base_df

        except Exception as e:
            self.logger.error(f"馬基本情報統合エラー: {e}")
            return base_df

    def _extract_horse_ids_from_race_data(self, race_df: pd.DataFrame) -> List[str]:
        """
        レースデータから馬IDを抽出

        Parameters
        ----------
        race_df : pd.DataFrame
            レースデータ

        Returns
        -------
        List[str]
            馬IDのリスト
        """
        try:
            if 'horse_id' not in race_df.columns:
                self.logger.warning("horse_idカラムが見つかりません")
                return []

            # ユニークな馬IDを抽出
            horse_ids = race_df['horse_id'].dropna().astype(str).unique().tolist()
            self.logger.info(f"レースから抽出した馬ID数: {len(horse_ids)}頭")
            return horse_ids

        except Exception as e:
            self.logger.error(f"馬ID抽出エラー: {e}")
            return []

    def _merge_horse_performance_stats(self, base_df: pd.DataFrame,
                                     performance_window_races: List[int],
                                     max_workers: Optional[int] = None,
                                     target_horse_ids: Optional[List[str]] = None) -> pd.DataFrame:
        """
        馬過去成績統計をベースデータに統合

        Parameters
        ----------
        base_df : pd.DataFrame
            ベースデータ
        performance_window_races : List[int]
            過去成績の集計対象レース数
        max_workers : int, optional (未使用、HorseProcessor側で利用)
            並列処理の最大ワーカー数 (HorseProcessorのデータ取得時に使用)
        target_horse_ids : List[str], optional
            処理対象の馬IDリスト。指定されていれば、これらの馬の過去成績のみ取得・計算する。

        Returns
        -------
        pd.DataFrame
            過去成績統計が統合されたデータ
        """
        self.logger.info("馬過去成績統計の統合を開始します。")
        if 'horse_id' not in base_df.columns:
            self.logger.error("ベースデータに 'horse_id' カラムが見つかりません。")
            return base_df
        if 'date' not in base_df.columns:
            self.logger.error("ベースデータに 'date' (レース開催日) カラムが見つかりません。")
            return base_df

        if not target_horse_ids:
            self.logger.warning("過去成績統計の対象となる馬がいません。")
            return base_df

        # HorseProcessorから全馬の過去成績データを取得
        # ここでは、必要な期間や馬IDに絞り込む前の全データを取得すると仮定
        # 実際には、HorseProcessorのget_all_horse_resultsやget_horse_results_by_yearsなどを利用
        # ここでは、RaceProcessorのmerge_past_horse_performanceのロジックを参考に、
        # HorseProcessorのpreprocessed_data (全馬の過去成績と仮定) を利用する。
        all_past_horse_results_df = self.horse_processor.preprocessed_data
        all_past_horse_results_df = self._standardize_data_types(all_past_horse_results_df)

        if all_past_horse_results_df.empty:
            self.logger.warning("馬過去成績データが空のため、過去成績統計の統合をスキップします")
            return base_df

        # 型変換とカラム名統一
        base_df['horse_id'] = base_df['horse_id'].astype(str)
        base_df['date'] = pd.to_datetime(base_df['date'], errors='coerce')

        if 'horse_id' not in all_past_horse_results_df.columns or 'date' not in all_past_horse_results_df.columns:
            self.logger.error("馬過去成績データに 'horse_id' または 'date' カラムが見つかりません。")
            return base_df

        # 処理対象の統計カラム
        # HorseResultsCols から参照するように変更を検討
        # constants.HorseResultsCols の Enumメンバーの .value を使用して列名を参照
        # このインポートはモジュールレベルに移動済みと仮定
        from module.constants import HorseResultsCols # dataclassなので .value は不要
        potential_stat_cols = [
            HorseResultsCols.RANK, HorseResultsCols.POPULARITY,
            HorseResultsCols.TANSHO_ODDS, HorseResultsCols.PRIZE,
            HorseResultsCols.KINRYO, HorseResultsCols.TIME, # TIMEは秒に変換後が望ましい
            HorseResultsCols.NOBORI, # 上がりタイム
            "体重", "体重変化" # これらは _preprocess_horse_results で生成される想定
        ]
        stat_target_cols = [col for col in potential_stat_cols if col in all_past_horse_results_df.columns]
        
        for col_name in stat_target_cols:
            all_past_horse_results_df[col_name] = pd.to_numeric(all_past_horse_results_df[col_name], errors='coerce')

        if not stat_target_cols:
            self.logger.warning("過去成績データに統計計算可能な数値カラムが見つかりません。")
            return base_df

        self.logger.info(f"過去成績の統計計算対象カラム: {stat_target_cols}")

        # RaceProcessorのmerge_past_horse_performanceメソッドを呼び出して過去成績をマージ
        # このメソッドは current_race_results_df と all_horse_past_results_df を取る
        # target_cols は stat_target_cols を使用
        # n_races_list は self.config.performance_window_races を使用
        # group_cols は RaceProcessor のデフォルトまたは設定に従う (ここではNoneとしておく)
        final_merged_df = self.race_processor.merge_past_horse_performance(
            current_race_results_df=base_df,
            all_horse_past_results_df=all_past_horse_results_df,
            target_cols=stat_target_cols,
            n_races_list=performance_window_races,
            group_cols=None # 必要に応じて設定
        )


        self.logger.info(f"馬過去成績統計の統合が完了しました。処理後の行数: {len(final_merged_df)}")
        return final_merged_df

    def save_comprehensive_table(self,
                                filename_prefix: str = "comprehensive_race_data",
                                year: Optional[str] = None,
                                save_pickle: bool = True,
                                save_csv: bool = True) -> Tuple[Optional[str], Optional[str]]:
        """
        包括的な統合データをファイルに保存

        Parameters
        ----------
        filename_prefix : str, default "comprehensive_race_data"
            ファイル名のプレフィックス
        year : str, optional
            年度（ファイル名に含める）
        save_pickle : bool, default True
            Pickleファイルとして保存するか
        save_csv : bool, default True
            CSVファイルとして保存するか

        Returns
        -------
        Tuple[Optional[str], Optional[str]]
            (pickle_path, csv_path) 保存したファイルのパス
        """
        if self._comprehensive_df.empty:
            self.logger.warning("保存するデータがありません")
            return None, None

        # 保存先ディレクトリを作成
        csv_dir = os.path.join(LocalPaths.DATA_DIR, "csv")
        os.makedirs(csv_dir, exist_ok=True)

        # ファイル名を生成
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        year_str = f"_{year}" if year else ""

        pickle_path = None
        csv_path = None

        # Pickleファイルとして保存
        if save_pickle:
            pickle_filename = f"{filename_prefix}{year_str}_{timestamp}.pickle"
            pickle_path = os.path.join(csv_dir, pickle_filename)
            self._comprehensive_df.to_pickle(pickle_path)
            self.logger.info(f"Pickleファイルを保存しました: {pickle_path}")

        # CSVファイルとして保存
        if save_csv:
            csv_filename = f"{filename_prefix}{year_str}_{timestamp}.csv"
            csv_path = os.path.join(csv_dir, csv_filename)
            self._comprehensive_df.to_csv(csv_path, index=False, encoding="utf-8-sig")
            self.logger.info(f"CSVファイルを保存しました: {csv_path}")

        return pickle_path, csv_path

    def get_data_summary(self) -> Dict[str, Any]:
        """
        統合データの概要情報を取得

        Returns
        -------
        Dict[str, Any]
            データ概要情報
        """
        if self._comprehensive_df.empty:
            return {"status": "empty", "message": "データが生成されていません"}

        df = self._comprehensive_df

        summary = {
            "status": "success",
            "total_records": len(df),
            "total_columns": len(df.columns),
            "unique_races": df['race_id'].nunique() if 'race_id' in df.columns else 0,
            "unique_horses": df['horse_id'].nunique() if 'horse_id' in df.columns else 0,
            "data_columns": {
                "race_info_columns": [col for col in df.columns if any(keyword in col.lower() for keyword in ['天気', '馬場', '距離', 'コース', 'レース'])],
                "horse_info_columns": [col for col in df.columns if any(keyword in col.lower() for keyword in ['father', 'mother', '調教師', '馬主', '生年月日'])],
                "performance_columns": [col for col in df.columns if any(keyword in col.lower() for keyword in ['last_', 'mean', 'std', 'min', 'max'])],
                "basic_columns": [col for col in df.columns if col in ['race_id', 'horse_id', '着順', '馬名', '騎手', '斤量', 'オッズ', '人気']]
            },
            "missing_data_ratio": (df.isnull().sum() / len(df)).round(3).to_dict()
        }

        return summary

    @property
    def comprehensive_data(self) -> pd.DataFrame:
        """
        生成された包括的データを取得

        Returns
        -------
        pd.DataFrame
            包括的な統合データ
        """
        return self._comprehensive_df.copy() if not self._comprehensive_df.empty else pd.DataFrame()

    def _standardize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """データ型を統一する。DataFrameが空の場合はそのまま返す。"""
        if df.empty:
            return df
        
        id_columns = ['horse_id', 'race_id', 'jockey_id', 'trainer_id', 
                      'father_id', 'mother_id', 'mother_father_id'] # HorseInfoCols からも参照する
        
        for col in id_columns:
            if col in df.columns:
                try:
                    # pd.StringDtype() を使用して、欠損値を pd.NA として扱う
                    df[col] = df[col].astype(pd.StringDtype())
                except TypeError:
                    # 古いPandasバージョンなど StringDtype が使えない場合のフォールバック
                    # object型にしてから文字列にし、'nan'やNoneを適切に処理
                    df[col] = df[col].astype(object).fillna(pd.NA).astype(str).replace({'<NA>': pd.NA, 'nan': pd.NA, 'None': pd.NA})
                    # 空文字にしたい場合は fillna('') とする
                    # df[col] = df[col].astype(object).fillna('').astype(str)

        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
        # 他の日付関連カラムも同様に (例: '生年月日')
        if '生年月日' in df.columns: # HorseInfoCols.BIRTHDAY
            df['生年月日'] = pd.to_datetime(df['生年月日'], errors='coerce')

        return df

def load_config_from_file(config_path: Optional[str]) -> Dict[str, Any]:
    """設定ファイルを読み込むヘルパー関数"""
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logging.error(f"設定ファイル {config_path} のJSON形式が不正です: {e}")
        except IOError as e:
            logging.error(f"設定ファイル {config_path} の読み込みに失敗しました: {e}")
    return {}

def merge_settings(file_config: Dict[str, Any], args: argparse.Namespace) -> Dict[str, Any]:
    """設定ファイルとコマンドライン引数をマージする"""
    settings = file_config.copy()

    # コマンドライン引数が指定されていれば、それで上書き
    if args.year: settings['year'] = args.year
    if args.years: settings['years'] = args.years
    if args.race_id: settings['race_id'] = args.race_id
    if args.no_race_info: settings['include_race_info'] = False
    if args.no_horse_info: settings['include_horse_info'] = False
    if args.no_past_performance: settings['include_past_performance'] = False
    if args.performance_windows: settings['performance_window_races'] = args.performance_windows
    if args.no_parallel: settings['parallel'] = False
    if args.workers is not None: settings['max_workers'] = args.workers # Noneでない場合のみ上書き
    if args.save is not None: settings['save_output'] = args.save # store_true/store_false のため
    if args.filename_prefix: settings['filename_prefix'] = args.filename_prefix
    # integrator_options は設定ファイルからのみ取得する例（必要なら引数追加も可）
    return settings

def main():
    """
    メイン実行関数
    """
    import argparse
    # ロギング設定
    logging.basicConfig(level=logging.INFO,
                       format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                       handlers=[logging.StreamHandler()])

    parser = argparse.ArgumentParser(description="包括的な競馬データ統合表を生成")
    parser.add_argument("--year", type=str, help="処理する年度（単一年度）")
    parser.add_argument("--years", nargs="*", type=str, help="処理する年度（複数年度）。指定しない場合は設定ファイルの値を使用。") # nargs="*" に変更
    parser.add_argument("--race-id", type=str, help="処理する特定のレースID")
    parser.add_argument("--no-race-info", action="store_true", help="レース情報を含めない")
    parser.add_argument("--no-horse-info", action="store_true", help="馬基本情報を含めない")
    parser.add_argument("--no-past-performance", action="store_true", help="馬過去成績統計を含めない")
    parser.add_argument("--performance-windows", nargs="*", type=int, # nargs="*" に変更
                       help="過去成績の集計対象レース数")
    parser.add_argument("--no-parallel", action="store_true", help="並列処理を使用しない (デフォルトは設定ファイルに従う)")
    parser.add_argument("--workers", type=int, help="並列処理の最大ワーカー数。指定しない場合は設定ファイルまたはデフォルト値。")
    parser.add_argument("--save", action=argparse.BooleanOptionalAction, default=None, help="結果をファイルに保存する/しない。指定なしは設定ファイルに従う。") # BooleanOptionalAction
    parser.add_argument("--filename-prefix", type=str,
                       help="保存ファイル名のプレフィックス")
    parser.add_argument("--config-file", type=str, default="config.json", help="設定ファイルのパス (default: config.json)")

    args = parser.parse_args()

    # 設定ファイルの読み込み
    file_config = load_config_from_file(args.config_file)
    if file_config:
        logging.info(f"設定ファイル {args.config_file} を読み込みました。")
    else:
        logging.info(f"設定ファイル {args.config_file} は読み込まれなかったか、空です。デフォルト設定またはコマンドライン引数を使用します。")

    # 設定のマージ
    settings = merge_settings(file_config, args) # type: ignore
    logging.info(f"最終的な実行設定: {settings}")

    # ComprehensiveDataIntegratorのインスタンスを作成
    integrator = ComprehensiveDataIntegrator(config=settings) # マージ済みの設定を渡す
    
    # 包括的データを生成
    comprehensive_df = integrator.generate_comprehensive_table(
        year=settings.get('year'), # type: ignore
        years=settings.get('years'), # type: ignore
        race_id=settings.get('race_id'), # type: ignore
        include_race_info=settings.get('include_race_info', integrator.config.include_race_info),
        include_horse_info=settings.get('include_horse_info', integrator.config.include_horse_info),
        include_past_performance=settings.get('include_past_performance', integrator.config.include_past_performance),
        performance_window_races=settings.get('performance_window_races', integrator.config.performance_window_races),
        parallel=settings.get('parallel', integrator.config.parallel),
        max_workers=settings.get('max_workers')
    )

    # 結果を表示
    if not comprehensive_df.empty:
        integrator.logger.info(f"\n✅ 包括的データ生成完了:")
        integrator.logger.info(f"   データ件数: {len(comprehensive_df):,}件")
        integrator.logger.info(f"   カラム数: {len(comprehensive_df.columns)}個")
        if 'race_id' in comprehensive_df.columns:
            integrator.logger.info(f"   ユニークレース数: {comprehensive_df['race_id'].nunique()}")
        if 'horse_id' in comprehensive_df.columns:
            integrator.logger.info(f"   ユニーク馬数: {comprehensive_df['horse_id'].nunique()}")

        # データ概要を表示
        summary = integrator.get_data_summary()
        integrator.logger.info(f"\n📊 データ構成:")
        for category, columns in summary["data_columns"].items():
            if columns:
                integrator.logger.info(f"   {category}: {len(columns)}個")

        integrator.logger.info(f"\n📋 サンプルデータ:")
        integrator.logger.info(f"\n{comprehensive_df.head()}") # DataFrameの表示は改行を入れる

        # ファイルに保存
        if settings.get('save_output', False):
            # 複数年度の場合はファイル名に年度範囲を含める
            current_years = settings.get('years', [])
            if isinstance(current_years, list) and len(current_years) > 1:
                year_range_str = f"{min(current_years)}-{max(current_years)}"
                filename_prefix_for_save = f"{settings.get('filename_prefix', integrator.config.filename_prefix)}_multi_year_{year_range_str}"
                save_year = None
            else:
                filename_prefix_for_save = settings.get('filename_prefix', integrator.config.filename_prefix)
                save_year = current_years[0] if isinstance(current_years, list) and current_years else settings.get('year', integrator.config.default_year)
            pickle_path, csv_path = integrator.save_comprehensive_table(
                filename_prefix=filename_prefix_for_save,
                year=save_year
            )
            integrator.logger.info(f"\n💾 ファイル保存完了:")
            if pickle_path:
                integrator.logger.info(f"   Pickle: {pickle_path}")
            if csv_path:
                integrator.logger.info(f"   CSV: {csv_path}")
    else:
        integrator.logger.error("❌ データの生成に失敗しました")


if __name__ == "__main__":
    main()
