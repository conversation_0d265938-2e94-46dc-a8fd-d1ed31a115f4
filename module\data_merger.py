import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import pandas as pd
from tqdm.auto import tqdm

from module.constants import DataMergerConstants, RaceInfoCols, HorseInfoCols # LocalPaths は未使用


class DataMerger:
    """
    レース情報、馬情報、過去成績データを統合してマージするクラス

    現在のプロジェクトのRaceProcessorとHorseProcessorに対応
    """

    @staticmethod
    def _normalize_date(date_input: Any) -> Optional[str]:
        """
        日付入力（文字列、数値、datetimeオブジェクトなど）を統一形式（YYYY-MM-DD）に変換します。
        変換できない場合はNoneを返します。

        Parameters
        ----------
        date_input : Any
            日付入力（文字列、数値、datetimeオブジェクトなど）

        Returns
        -------
        Optional[str]
            統一形式（YYYY-MM-DD）の日付文字列、または変換できなかった場合はNone
        """
        if pd.isna(date_input) or date_input == '':
            return None

        # datetimeオブジェクトの場合は直接フォーマット
        if isinstance(date_input, datetime):
            return date_input.strftime('%Y-%m-%d')
        
        # 数値の場合は文字列に変換して処理
        if isinstance(date_input, (int, float)):
            date_str = str(int(date_input)) # 小数点以下を切り捨てて整数に
        else:
            date_str = str(date_input)

        try:
            # YYYY/MM/DD 形式の場合
            if '/' in date_str:
                date_obj = datetime.strptime(date_str, '%Y/%m/%d')
                return date_obj.strftime('%Y-%m-%d')
            # YYYY-MM-DD 形式の場合
            elif '-' in date_str:
                # 形式チェックのみ
                datetime.strptime(date_str, '%Y-%m-%d')
                return date_str
            # YYYYMMDD 形式の場合
            elif len(date_str) == 8 and date_str.isdigit():
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                return date_obj.strftime('%Y-%m-%d')
            else:
                # その他の形式は変換を試みずNone
                return None
        except ValueError:
            # 変換できない場合はNoneを返す
            return None

    def __init__(
        self,
        race_processor=None,
        horse_processor=None,
        target_cols: Optional[List[str]] = None,
        group_cols: Optional[List[str]] = None,
    ):
        """
        初期処理

        Parameters
        ----------
        race_processor : RaceProcessor
            レース情報と結果を処理するプロセッサ
        horse_processor : HorseProcessor
            馬の基本情報と過去成績を処理するプロセッサ
        target_cols : List[str], optional
            集計対象列（馬の成績として扱う項目）
        group_cols : List[str], optional
            horse_idと一緒にターゲットエンコーディングしたいカテゴリ変数
        """
        self.logger = logging.getLogger(__name__)

        # プロセッサの設定
        self._race_processor = race_processor
        self._horse_processor = horse_processor

        # データの初期化
        self._race_info = pd.DataFrame()
        self._race_results = pd.DataFrame()
        self._horse_info = pd.DataFrame()
        self._horse_results = pd.DataFrame()

        # プロセッサからデータを取得
        if race_processor is not None:
            # RaceProcessorから前処理済みデータを取得
            try:
                preprocessed = race_processor.preprocess_data()
                if not preprocessed.empty:
                    # dateカラムの確認・作成（最優先で実行）
                    if 'date' not in preprocessed.columns:
                        if 'race_id' in preprocessed.columns:
                            preprocessed['date'] = pd.to_datetime(preprocessed['race_id'].astype(str).str[:8], format='%Y%m%d', errors='coerce')
                            self.logger.info("RaceProcessorデータに'race_id'から'date'カラムを作成しました")
                        elif '日付' in preprocessed.columns:
                            preprocessed['date'] = pd.to_datetime(preprocessed['日付'], errors='coerce')
                            self.logger.info("RaceProcessorデータに'日付'から'date'カラムを作成しました")
                        else:
                            self.logger.warning("RaceProcessorデータにdateカラムを作成できませんでした")
                    else:
                        # 既存のdateカラムを正規化
                        preprocessed['date'] = pd.to_datetime(preprocessed['date'], errors='coerce')
                        self.logger.info("RaceProcessorデータの既存dateカラムを正規化しました")

                    # レース情報とレース結果を分離
                    race_info_cols = [RaceInfoCols.RACE_ID, RaceInfoCols.DATE, RaceInfoCols.VENUE, RaceInfoCols.RACE_NAME, RaceInfoCols.RACE_TYPE,
                                    RaceInfoCols.DISTANCE, RaceInfoCols.WEATHER, RaceInfoCols.GROUND_STATE, RaceInfoCols.AROUND]
                    available_race_cols = [col for col in race_info_cols if col in preprocessed.columns]

                    if available_race_cols and 'race_id' in available_race_cols:
                        self._race_info = preprocessed[available_race_cols].drop_duplicates(subset=['race_id'])
                        self._race_info = self._race_info.set_index('race_id')

                    # レース結果（出走馬情報）
                    self._race_results = preprocessed.copy()

                    # レース結果データの日付も正規化
                    if 'date' in self._race_results.columns:
                        self._race_results['date'] = pd.to_datetime(self._race_results['date'], errors='coerce')
                        self.logger.info("レース結果データの日付形式を統一しました")

                    if 'race_id' in self._race_results.columns:
                        self._race_results = self._race_results.set_index('race_id')
            except Exception as e:
                self.logger.warning(f"RaceProcessorからのデータ取得に失敗: {e}")
                # フォールバック: 個別のデータがある場合
                if hasattr(race_processor, '_race_info_df') and not race_processor._race_info_df.empty:
                    self._race_info = race_processor._race_info_df.copy()
                    if 'race_id' in self._race_info.columns:
                        self._race_info = self._race_info.set_index('race_id')

                if hasattr(race_processor, '_race_results_df') and not race_processor._race_results_df.empty:
                    self._race_results = race_processor._race_results_df.copy()
                    if 'race_id' in self._race_results.columns:
                        self._race_results = self._race_results.set_index('race_id')

        if horse_processor is not None:
            if hasattr(horse_processor, 'preprocessed_data'):
                preprocessed = horse_processor.preprocessed_data
                if not preprocessed.empty:
                    self.logger.info(f"HorseProcessor前処理済みデータ: {len(preprocessed)}件")
                    self.logger.info(f"HorseProcessor前処理済みデータのカラム: {preprocessed.columns.tolist()}")

                    # dateカラムの確認・作成
                    if 'date' not in preprocessed.columns:
                        if '日付' in preprocessed.columns:
                            preprocessed['date'] = pd.to_datetime(preprocessed['日付'], errors='coerce')
                            self.logger.info("HorseProcessorデータに'日付'から'date'カラムを作成しました")
                        elif 'race_id' in preprocessed.columns:
                            preprocessed['date'] = pd.to_datetime(preprocessed['race_id'].astype(str).str[:8], format='%Y%m%d', errors='coerce')
                            self.logger.info("HorseProcessorデータに'race_id'から'date'カラムを作成しました")
                        else:
                            self.logger.warning("HorseProcessorデータにdateカラムを作成できませんでした")
                    else:
                        # 既存のdateカラムを正規化
                        preprocessed['date'] = pd.to_datetime(preprocessed['date'], errors='coerce')
                        self.logger.info("HorseProcessorデータの既存dateカラムを正規化しました")

                    # 馬の基本情報と過去成績を分離（母父情報を追加）
                    horse_info_cols = [HorseInfoCols.HORSE_NAME, # horse_id はインデックスになる想定
                                       HorseInfoCols.BIRTHDAY, HorseInfoCols.RELATIVE_HORSE,
                                       HorseInfoCols.FATHER_NAME, HorseInfoCols.MOTHER_NAME, HorseInfoCols.MOTHER_FATHER_NAME,
                                       HorseInfoCols.FATHER_ID, HorseInfoCols.MOTHER_ID, HorseInfoCols.MOTHER_FATHER_ID, HorseInfoCols.TRAINER, HorseInfoCols.OWNER]
                    available_horse_cols = [col for col in horse_info_cols if col in preprocessed.columns]

                    if available_horse_cols and 'horse_id' in available_horse_cols:
                        self._horse_info = preprocessed[available_horse_cols].drop_duplicates(subset=['horse_id'])
                        self._horse_info = self._horse_info.set_index('horse_id')
                        self.logger.info(f"馬の基本情報を抽出: {len(self._horse_info)}件")

                    # 過去成績データ（dateまたは日付とhorse_idがある行）
                    date_col = None
                    if 'date' in preprocessed.columns:
                        date_col = 'date'
                    elif '日付' in preprocessed.columns:
                        date_col = '日付'
                        # 日付カラムをdateカラムとしてコピー
                        preprocessed['date'] = preprocessed['日付']
                        self.logger.info("日付カラムをdateカラムとしてコピーしました")

                    if date_col and 'horse_id' in preprocessed.columns:
                        # dateがNaNでない行のみを過去成績として扱う
                        self._horse_results = preprocessed[preprocessed['date'].notna()].copy()

                        # 日付の形式を統一
                        if 'date' in self._horse_results.columns:
                            self._horse_results['date'] = pd.to_datetime(self._horse_results['date'], errors='coerce')
                            self.logger.info("過去成績データの日付形式を統一しました")

                        if 'horse_id' in self._horse_results.columns:
                            self._horse_results = self._horse_results.set_index('horse_id')
                        self.logger.info(f"馬の過去成績を抽出: {len(self._horse_results)}件")

                        # 過去成績データの内容を確認
                        if not self._horse_results.empty:
                            self.logger.info(f"過去成績データのカラム: {self._horse_results.columns.tolist()}")
                            self.logger.info(f"過去成績データのサンプル: {self._horse_results.head(2).to_dict()}")
                    else:
                        self.logger.warning(f"dateまたは日付カラムが見つかりません。利用可能なカラム: {preprocessed.columns.tolist()}")

        # 集計対象列のデフォルト設定
        if target_cols is None:
            self._target_cols = list(DataMergerConstants.DEFAULT_TARGET_COLS)  # デフォルトの集計対象列
        else:
            self._target_cols = target_cols

        self.logger.info(f"集計対象列: {self._target_cols}")

        # グループ化列のデフォルト設定
        if group_cols is None:
            self._group_cols = list(DataMergerConstants.DEFAULT_GROUP_COLS)  # デフォルトのグループ化列
        else:
            self._group_cols = group_cols

        # 全てのマージが完了したデータ
        self._merged_data = pd.DataFrame()

        # 日付(date列)ごとに分かれたレース結果
        self._separated_results_dict = {}

        # レース結果データのdateごとに分かれた馬の過去成績
        self._separated_horse_results_dict = {}

        self.logger.info("DataMerger初期化完了")



    def merge(self):
        """
        マージ処理を実行
        """
        self.logger.info("データマージ処理を開始します")

        # レース結果をベースとして開始
        if self._race_results.empty:
            self.logger.warning("レース結果データが空のため、マージ処理を中止します")
            return

        self._merged_data = self._race_results.copy()

        # マージデータのdateカラムを確認・作成
        if 'date' not in self._merged_data.columns:
            if 'race_id' in self._merged_data.columns:
                # インデックスがrace_idの場合
                if self._merged_data.index.name == 'race_id':
                    self._merged_data['date'] = pd.to_datetime(self._merged_data.index.astype(str).str[:8], format='%Y%m%d', errors='coerce')
                else:
                    self._merged_data['date'] = pd.to_datetime(self._merged_data['race_id'].astype(str).str[:8], format='%Y%m%d', errors='coerce')
                self.logger.info("マージデータにrace_idからdateカラムを作成しました")
            else:
                self.logger.warning("マージデータにdateカラムを作成できませんでした")
        else:
            self.logger.info("マージデータに既にdateカラムが存在します")

        # レース情報をマージ
        self._merge_race_info()

        # 馬の過去成績をマージ（時系列を考慮）
        self.logger.info(f"馬の過去成績データ確認: {len(self._horse_results)}件")
        if not self._horse_results.empty:
            self.logger.info("馬の過去成績マージを開始します")
            self._merge_horse_results()
        else:
            self.logger.warning("馬の過去成績データが空のため、過去成績のマージをスキップします")

        # 馬の基本情報をマージ
        if not self._horse_info.empty:
            self._merge_horse_info()
        else:
            self.logger.warning("馬の基本情報データが空のため、馬情報のマージをスキップします")

        self.logger.info(f"マージ処理完了。最終データ: {len(self._merged_data)}件")

    def _merge_race_info(self):
        """
        レース情報テーブルを、レース結果テーブルにマージ
        """
        if self._race_info.empty:
            self.logger.warning("レース情報データが空のため、レース情報のマージをスキップします")
            return

        self.logger.info("レース情報をマージ中...")

        # インデックスベースでマージ（race_idで結合）
        self._merged_data = self._merged_data.merge(
            self._race_info,
            left_index=True,
            right_index=True,
            how='left'
        )

        self.logger.info(f"レース情報マージ後: {len(self._merged_data)}件")

    def _separate_by_date(self):
        """
        レース結果を日付(date列)ごとに分離し、
        各日付のレース結果と、その日に走る馬の過去成績データのペアを作成します。
        """
        self.logger.info('レース結果を日付ごとに分離中...')

        # dateカラムの存在確認と作成
        if 'date' not in self._merged_data.columns:
            self.logger.warning("dateカラムが存在しません。作成を試行します...")

            # race_idからdateカラムを作成
            if 'race_id' in self._merged_data.columns:
                try:
                    self._merged_data['date'] = pd.to_datetime(
                        self._merged_data['race_id'].astype(str).str[:8],
                        format='%Y%m%d',
                        errors='coerce'
                    )
                    self.logger.info("race_idからdateカラムを作成しました")
                except Exception as e:
                    self.logger.error(f"race_idからdateカラムの作成に失敗: {e}")
                    return
            else:
                self.logger.error("dateカラムもrace_idカラムも存在しないため、日付による分離をスキップします")
                return

        # 日付形式を統一
        self._merged_data['date'] = pd.to_datetime(self._merged_data['date'], errors='coerce')
        self.logger.info("マージデータの日付形式を統一しました")

        # 馬過去成績データの準備（参考実装に合わせる）
        if not self._horse_results.empty:
            # horse_idをインデックスに設定（まだ設定されていない場合）
            if self._horse_results.index.name != 'horse_id' and 'horse_id' in self._horse_results.columns:
                self._horse_results = self._horse_results.set_index('horse_id')
                self.logger.info("馬過去成績データのhorse_idをインデックスに設定しました")

        # dateでデータを分割（参考実装と同じ方式）
        self.logger.info('separating horse results by date')
        for date, df_by_date in tqdm(self._merged_data.groupby('date'), desc="日付毎のレース結果分離"):
            self._separated_results_dict[date] = df_by_date
            # その日に走る馬一覧
            horse_id_list = df_by_date['horse_id'].unique()

            # dateより過去に絞る（参考実装と同じquery方式）
            if not self._horse_results.empty and 'date' in self._horse_results.columns:
                try:
                    # 参考実装と同じquery方式を使用
                    past_results = self._horse_results.query('date < @date').query('index in @horse_id_list')
                    self._separated_horse_results_dict[date] = past_results
                    self.logger.debug(f"日付 {date}: {len(past_results)}件の過去成績を取得")
                except Exception as e:
                    self.logger.warning(f"日付 {date} のquery処理でエラー: {e}")
                    # フォールバック処理
                    past_results = self._horse_results[
                        (self._horse_results['date'] < date) &
                        (self._horse_results.index.isin(horse_id_list))
                    ]
                    self._separated_horse_results_dict[date] = past_results
                    self.logger.debug(f"日付 {date}: {len(past_results)}件の過去成績を取得（フォールバック）")
            else:
                self._separated_horse_results_dict[date] = pd.DataFrame()

        self.logger.info(f"日付分離完了: {len(self._separated_results_dict)}個の日付、{len(self._separated_horse_results_dict)}個の過去成績グループ")

    def _merge_horse_results(self, n_races_list: List[int] = [5, 9]):
        """
        馬の過去成績テーブルのマージ

        Parameters
        ----------
        n_races_list : List[int]
            集計対象とする直近レース数のリスト
        """
        self.logger.info(f'馬の過去成績をマージ中... 過去成績データ: {len(self._horse_results)}件')

        # 過去成績データが空の場合は処理をスキップ
        if self._horse_results.empty:
            self.logger.warning("馬の過去成績データが空のため、過去成績のマージをスキップします")
            return

        # デバッグ情報を追加
        self.logger.info(f"馬過去成績データのカラム: {self._horse_results.columns.tolist()}")
        self.logger.info(f"マージデータのカラム: {self._merged_data.columns.tolist()}")

        # 日付カラムの存在確認
        if 'date' in self._horse_results.columns:
            self.logger.info(f"馬過去成績の日付範囲: {self._horse_results['date'].min()} 〜 {self._horse_results['date'].max()}")
        else:
            self.logger.warning("馬過去成績データにdateカラムがありません")

        if 'date' in self._merged_data.columns:
            self.logger.info(f"マージデータの日付範囲: {self._merged_data['date'].min()} 〜 {self._merged_data['date'].max()}")
        else:
            self.logger.warning("マージデータにdateカラムがありません")

        self._separate_by_date()

        self.logger.info('merging horse_results')
        output_results_dict = {}

        for date in tqdm(self._separated_results_dict, desc="馬過去成績マージ (日付毎)"):
            results = self._separated_results_dict[date].copy()
            horse_results = self._separated_horse_results_dict[date].copy()

            # 直近nレースに絞った過去成績をマージ（参考実装と同じ方式）
            for n_races in n_races_list:
                # 直近nレースに絞った過去成績
                n_race_horse_results = self._filter_horse_results(horse_results, n_races)

                # horse_idのみのターゲットエンコーディング
                # 何レース分集計しているか分かるように、列名に接尾辞をつける
                summarized = self._summarize(n_race_horse_results, self._target_cols).add_suffix(f'_{n_races}R')
                # resultsにマージ
                results = results.merge(summarized, left_on='horse_id', right_index=True, how='left')

                # horse_idとカテゴリ変数を合わせてターゲットエンコーディング
                for group_col in self._group_cols:
                    # 何レース分、どのカテゴリ変数とともに集計しているか分かるように、列名に接尾辞をつける
                    summarized_with = self._summarize_with(n_race_horse_results, self._target_cols, group_col).add_suffix(f'_{group_col}_{n_races}R')
                    # resultsにマージ
                    results = results.merge(summarized_with, left_on=['horse_id', group_col], right_index=True, how='left')

            # 直近nレースに絞らないで過去成績をマージ
            summarized = self._summarize(horse_results, self._target_cols).add_suffix('_allR')
            results = results.merge(summarized, left_on='horse_id', right_index=True, how='left')

            for group_col in self._group_cols:
                # どのカテゴリ変数とともに集計しているか分かるように、列名に接尾辞をつける
                summarized_with = self._summarize_with(horse_results, self._target_cols, group_col).add_suffix(f'_{group_col}_allR')
                # resultsにマージ
                results = results.merge(summarized_with, left_on=['horse_id', group_col], right_index=True, how='left')

            # 前走の日付をマージ
            latest = horse_results.groupby('horse_id')['date'].max().rename('latest')
            results = results.merge(latest, left_on='horse_id', right_index=True, how='left')

            # 辞書型に格納
            output_results_dict[date] = results

        # 日付で分かれていたものを結合
        if output_results_dict:
            merged_data = pd.concat([output_results_dict[date] for date in output_results_dict])
            self._merged_data = merged_data
            self.logger.info(f"馬過去成績マージ後: {len(self._merged_data)}件")
        else:
            self.logger.warning("output_results_dictが空のため、過去成績のマージをスキップします")
            self.logger.info("日付による分離が正常に実行されなかった可能性があります")

    def _merge_horse_info(self):
        """
        馬の基本情報テーブルのマージ
        """
        if self._horse_info.empty:
            self.logger.warning("馬の基本情報データが空のため、馬情報のマージをスキップします")
            return

        self.logger.info("馬の基本情報をマージ中...")

        # horse_idの型を統一（文字列型に変換）
        if 'horse_id' in self._merged_data.columns:
            self._merged_data['horse_id'] = self._merged_data['horse_id'].astype(str)

        # インデックスのhorse_idも文字列型に変換
        if self._horse_info.index.name == 'horse_id':
            self._horse_info.index = self._horse_info.index.astype(str)

        # horse_idでマージ
        self._merged_data = self._merged_data.merge(
            self._horse_info,
            left_on='horse_id',
            right_index=True,
            how='left'
        )

        self.logger.info(f"馬基本情報マージ後: {len(self._merged_data)}件")

    @property
    def merged_data(self):
        """
        マージされたデータを取得

        Returns
        -------
        pd.DataFrame
            マージされたデータ
        """
        return self._merged_data.copy()

    def _filter_horse_results(self, horse_results: pd.DataFrame, n_races: int) -> pd.DataFrame:
        """
        直近nレースに絞る（参考実装と同じ方式）

        Parameters
        ----------
        horse_results : pd.DataFrame
            馬の過去成績データ
        n_races : int
            取得する直近レース数

        Returns
        -------
        pd.DataFrame
            直近nレースに絞られた過去成績データ
        """
        if horse_results.empty:
            return horse_results

        # 参考実装と同じ方式：sort_values('date', ascending=False).groupby(level=0).head(n_races)
        if 'date' in horse_results.columns:
            return horse_results.sort_values('date', ascending=False).groupby(level=0).head(n_races)
        else:
            self.logger.warning("dateカラムが存在しないため、フィルタリングをスキップします")
            return horse_results

    def _summarize(self, horse_results: pd.DataFrame, target_cols: List[str]) -> pd.DataFrame:
        """
        horse_idごとに、target_colsを集計する（参考実装と同じ方式）

        Parameters
        ----------
        horse_results : pd.DataFrame
            馬の過去成績データ
        target_cols : List[str]
            集計対象の列名リスト

        Returns
        -------
        pd.DataFrame
            horse_idごとに集計されたデータ
        """
        if horse_results.empty:
            return pd.DataFrame()

        # 存在する列のみを対象とする
        available_cols = [col for col in target_cols if col in horse_results.columns]

        if not available_cols:
            self.logger.warning(f"集計対象列が存在しません: {target_cols}")
            return pd.DataFrame()

        try:
            # 参考実装と同じ方式：groupby(level=0)を使用
            return horse_results.groupby(level=0)[available_cols].mean()
        except Exception as e:
            self.logger.error(f"集計処理でエラーが発生しました: {e}")
            # フォールバック処理
            if horse_results.index.name == 'horse_id':
                return horse_results.groupby(level=0)[available_cols].mean()
            else:
                return horse_results.groupby('horse_id')[available_cols].mean()

    def _summarize_with(self, horse_results: pd.DataFrame, target_cols: List[str], group_col: str) -> pd.DataFrame:
        """
        horse_id, group_colごとにtarget_colsを集計する

        Parameters
        ----------
        horse_results : pd.DataFrame
            馬の過去成績データ
        target_cols : List[str]
            集計対象の列名リスト
        group_col : str
            グループ化に使用する列名

        Returns
        -------
        pd.DataFrame
            horse_idとgroup_colごとに集計されたデータ
        """
        if horse_results.empty or group_col not in horse_results.columns:
            return pd.DataFrame()

        # 存在する列のみを対象とする
        available_cols = [col for col in target_cols if col in horse_results.columns]

        if not available_cols:
            self.logger.warning(f"集計対象列が存在しません: {target_cols}")
            return pd.DataFrame()

        try:
            if horse_results.index.name == 'horse_id':
                # インデックスがhorse_idの場合、resetしてからgroupby
                df_reset = horse_results.reset_index()
                return df_reset.groupby(['horse_id', group_col])[available_cols].mean()
            else:
                # horse_idがカラムの場合
                return horse_results.groupby(['horse_id', group_col])[available_cols].mean()
        except Exception as e:
            self.logger.error(f"グループ集計処理でエラーが発生しました: {e}")
            return pd.DataFrame()

    def get_summary_info(self) -> Dict[str, Any]:
        """
        マージ処理の概要情報を取得

        Returns
        -------
        Dict[str, Any]
            処理概要の辞書
        """
        return {
            'race_info_count': len(self._race_info),
            'race_results_count': len(self._race_results),
            'horse_info_count': len(self._horse_info),
            'horse_results_count': len(self._horse_results),
            'merged_data_count': len(self._merged_data),
            'target_cols': self._target_cols,
            'group_cols': self._group_cols,
            'merged_columns': list(self._merged_data.columns) if not self._merged_data.empty else []
        }
